'use client';

import { useEffect } from 'react';
import { useAuthStore } from '@/store/accountStore';
import Image from 'next/image';

/**
 * 积分消耗显示组件
 * 显示当前用户的积分使用情况，UI参考Figma设计
 * 支持首次访问欢迎消息和积分使用状态切换
 */
export function CreditDisplay() {
  const { userInfo, hasPerformedBackgroundRemoval, checkAndUpdateDailyVisit } =
    useAuthStore();

  // 检查是否是今日首次访问
  useEffect(() => {
    checkAndUpdateDailyVisit();
  }, [checkAndUpdateDailyVisit]);

  // 从用户信息中获取积分数据
  const currentScore = userInfo?.score ?? 0;
  const totalScore = userInfo?.total_score ? parseInt(userInfo.total_score) : 0;
  const usedCredits = totalScore - currentScore;

  // 确定显示的文本内容
  const isFirstTimeToday = !hasPerformedBackgroundRemoval;

  let displayText;

  if (currentScore === 0) {
    // 积分用完时显示
    displayText =
      "You've reached your monthly usage limit. Upgrade to continue";
  } else if (isFirstTimeToday) {
    // 首次访问时显示
    displayText = (
      <>
        You&apos;ve received{' '}
        <span className='text-brand-orange font-bold'>{totalScore}</span> free
        AI credits today
      </>
    );
  } else {
    // 正常使用状态显示
    displayText = (
      <>
        <span className='text-brand-orange font-bold'>{usedCredits}/</span>
        <span className='text-brand-orange font-bold'>{totalScore}</span> free
        credits used
      </>
    );
  }

  return (
    <div className='bg-[rgba(255,204,3,0.1)] rounded-[100px] px-[10px] py-[10px] flex items-center justify-center gap-[10px] h-[40px]'>
      <div className='flex items-center gap-[8px]'>
        {/* AI 图标 */}
        <div className='w-[24px] h-[24px]'>
          {isFirstTimeToday ? (
            <Image
              src='/apps/icons/firstTimeToday.svg'
              alt='firstTimeToday'
              width={24}
              height={24}
            />
          ) : (
            <Image
              src='/apps/icons/brand.svg'
              alt='brand'
              width={24}
              height={24}
            />
          )}
        </div>

        {/* 文本内容 */}
        <div className='flex items-center gap-[8px]'>
          <span className='text-[#121212] text-[14px] font-normal leading-[1.5em]'>
            Advanced AI
          </span>

          {/* 分隔线 */}
          <div className='w-[1px] h-[12px] bg-[#D0D0D0]' />

          <span className='text-[#121212] text-[14px] font-normal leading-[1.5em]'>
            {displayText}
          </span>
        </div>
      </div>
    </div>
  );
}
