import type { ImageState } from '../store/imageStore';

// IndexedDB存储的图片元数据接口
export interface StoredImageMetadata {
  id: string;
  name: string;
  width: number;
  height: number;
  size: number;
  originalSize?: number; // 原始文件大小
  status:
    | 'original'
    | 'bg-removed'
    | 'compressed'
    | 'locked'
    | 'bg-remove-failed';
  backgroundColor: string;
  backgroundImageUrl?: string;
  backgroundImageId?: string; // 自定义背景图片的ID
  isBlurEnabled: boolean;
  blurAmount: number;
  isEraseMode: boolean;
  isRestoreMode: boolean;
  eraseBrushSize: number;
  eraseOperationCount: number;
  timestamp: number;
  // 文件存储信息
  hasOriginalFile: boolean;
  hasProcessedImage: boolean;
  originalFileChunks?: number;
  processedImageChunks?: number;
}

// 文件块接口
export interface FileChunk {
  id: string;
  imageId: string;
  chunkIndex: number;
  totalChunks: number;
  data: ArrayBuffer;
  type: 'original' | 'processed';
}

// 应用状态接口
export interface AppState {
  id: 'current'; // 固定ID，用于存储当前应用状态
  currentSelectedImageId: string | null;
  timestamp: number;
}

// 自定义背景图片接口
export interface StoredBackgroundImage {
  id: string;
  name: string;
  size: number;
  timestamp: number;
  hasFile: boolean;
  fileChunks?: number;
}

/**
 * IndexedDB图片存储管理类
 * 支持大文件分块存储和元数据管理
 */
export class ImageIndexedDBStorage {
  private dbName = 'PixPrettyImageDB';
  private version = 3; // 增加版本号以支持背景图片存储
  private db: IDBDatabase | null = null;
  private readonly CHUNK_SIZE = 1024 * 1024; // 1MB chunks
  private readonly SESSION_KEY = 'pixpretty_session_active';

  /**
   * 初始化数据库
   */
  async init(): Promise<void> {
    // 移除强制清理数据库的逻辑，改为温和的初始化方式
    // 这样可以避免多标签页时的阻塞问题
    if (
      typeof window !== 'undefined' &&
      !sessionStorage.getItem(this.SESSION_KEY)
    ) {
      sessionStorage.setItem(this.SESSION_KEY, 'true');
    }

    return new Promise<void>((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => {
        console.error('IndexedDB初始化失败:', request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;

        resolve();
      };

      request.onupgradeneeded = event => {
        const db = (event.target as IDBOpenDBRequest).result;

        // 图片元数据存储
        if (!db.objectStoreNames.contains('images')) {
          const imageStore = db.createObjectStore('images', { keyPath: 'id' });
          imageStore.createIndex('timestamp', 'timestamp', { unique: false });
          imageStore.createIndex('status', 'status', { unique: false });
        }

        // 文件块存储
        if (!db.objectStoreNames.contains('chunks')) {
          const chunkStore = db.createObjectStore('chunks', { keyPath: 'id' });
          chunkStore.createIndex('imageId', 'imageId', { unique: false });
          chunkStore.createIndex('type', 'type', { unique: false });
        }

        // 应用状态存储
        if (!db.objectStoreNames.contains('appState')) {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const appStateStore = db.createObjectStore('appState', {
            keyPath: 'id',
          });
        }

        // 自定义背景图片存储
        if (!db.objectStoreNames.contains('backgroundImages')) {
          const backgroundStore = db.createObjectStore('backgroundImages', {
            keyPath: 'id',
          });
          backgroundStore.createIndex('timestamp', 'timestamp', {
            unique: false,
          });
        }
      };
    });
  }

  /**
   * 清理整个数据库（仅在必要时使用）
   * 这个方法现在更加宽容，不会因为多标签页而阻塞应用启动
   */
  async clearDatabase(): Promise<void> {
    return new Promise(resolve => {
      // 如果有已打开的连接，必须先关闭
      if (this.db) {
        this.db.close();
        this.db = null;
      }

      const deleteRequest = indexedDB.deleteDatabase(this.dbName);

      deleteRequest.onsuccess = () => {
        resolve();
      };

      deleteRequest.onerror = event => {
        const error = (event.target as IDBOpenDBRequest).error;
        console.warn(
          `删除IndexedDB数据库 '${this.dbName}' 失败，但不影响应用正常运行`,
          error
        );
        // 即使删除失败也不阻塞应用启动
        resolve();
      };

      deleteRequest.onblocked = () => {
        console.warn(
          '删除数据库被阻塞，可能有其他标签页正在使用数据库。跳过清理操作，继续正常初始化。'
        );
        // 不再抛出错误，而是优雅地跳过清理操作
        resolve();
      };
    });
  }

  /**
   * 安全的数据库重置方法
   * 只清理当前会话的数据，不影响其他标签页
   */
  async safeClearCurrentSession(): Promise<void> {
    if (!this.db) {
      return;
    }

    try {
      // 只清理当前会话的数据，而不是删除整个数据库
      const transaction = this.db.transaction(
        ['images', 'chunks', 'backgroundImages'],
        'readwrite'
      );

      // 清理图片数据
      transaction.objectStore('images').clear();
      transaction.objectStore('chunks').clear();
      transaction.objectStore('backgroundImages').clear();
    } catch (error) {
      console.warn('清理当前会话数据失败，但不影响应用正常运行:', error);
    }
  }

  /**
   * 保存完整的图片状态到IndexedDB
   */
  async saveImage(imageState: ImageState): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    // 准备元数据
    const metadata: StoredImageMetadata = {
      id: imageState.id,
      name: imageState.name,
      width: imageState.width,
      height: imageState.height,
      size: imageState.size,
      originalSize: imageState.originalSize, // 保存原始文件大小
      status: imageState.status,
      backgroundColor: imageState.backgroundColor,
      backgroundImageUrl: imageState.backgroundImageUrl,
      backgroundImageId: imageState.backgroundImageId,
      isBlurEnabled: imageState.isBlurEnabled,
      blurAmount: imageState.blurAmount,
      isEraseMode: imageState.isEraseMode,
      isRestoreMode: imageState.isRestoreMode,
      eraseBrushSize: imageState.eraseBrushSize,
      eraseOperationCount: imageState.eraseOperationCount,
      timestamp: imageState.timestamp, // 使用图片的原始时间戳
      hasOriginalFile: false,
      hasProcessedImage: false,
    };

    const savePromises: Promise<void>[] = [];

    // 保存原始文件
    if (imageState.file) {
      const chunks = await this.saveFileInChunks(
        imageState.id,
        imageState.file,
        'original'
      );
      metadata.hasOriginalFile = true;
      metadata.originalFileChunks = chunks;
    }

    // 保存处理后的图片
    if (
      imageState.processedUrl &&
      imageState.processedUrl.startsWith('blob:')
    ) {
      savePromises.push(
        this.saveBlobUrlAsChunks(
          imageState.id,
          imageState.processedUrl,
          'processed'
        ).then(chunks => {
          metadata.hasProcessedImage = true;
          metadata.processedImageChunks = chunks;
        })
      );
    }

    // 等待所有文件保存完成
    await Promise.all(savePromises);

    // 保存元数据
    await this.saveMetadata(metadata);
  }

  /**
   * 从IndexedDB加载图片状态
   */
  async loadImage(imageId: string): Promise<ImageState | null> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    // 加载元数据
    const metadata = await this.loadMetadata(imageId);
    if (!metadata) {
      return null;
    }

    // 重建ImageState对象
    const imageState: ImageState = {
      id: metadata.id,
      name: metadata.name,
      width: metadata.width,
      height: metadata.height,
      size: metadata.size,
      originalSize: metadata.originalSize, // 恢复原始文件大小
      status: metadata.status,
      history: [], // 历史记录不持久化
      timestamp: metadata.timestamp, // 恢复时间戳
      backgroundColor: metadata.backgroundColor,
      backgroundImageUrl: metadata.backgroundImageUrl,
      backgroundImageId: metadata.backgroundImageId,
      isBlurEnabled: metadata.isBlurEnabled,
      blurAmount: metadata.blurAmount,
      isEraseMode: metadata.isEraseMode,
      isRestoreMode: metadata.isRestoreMode,
      eraseBrushSize: metadata.eraseBrushSize,
      eraseOperationCount: metadata.eraseOperationCount,
      previewUrl: '', // 稍后加载
      processedUrl: null,
      eraseHistory: [],
      currentEraseCanvasData: null,
      resizeMode: 'fill',
    };

    const loadPromises: Promise<void>[] = [];

    // 加载原始文件
    if (metadata.hasOriginalFile && metadata.originalFileChunks) {
      loadPromises.push(
        this.loadFileFromChunks(
          imageId,
          'original',
          metadata.originalFileChunks
        ).then(file => {
          imageState.file = file;
          imageState.previewUrl = URL.createObjectURL(file);
        })
      );
    }

    // 加载处理后的图片
    if (metadata.hasProcessedImage && metadata.processedImageChunks) {
      loadPromises.push(
        this.loadBlobFromChunks(
          imageId,
          'processed',
          metadata.processedImageChunks
        ).then(blob => {
          imageState.processedUrl = URL.createObjectURL(blob);
        })
      );
    }

    await Promise.all(loadPromises);

    return imageState;
  }

  /**
   * 删除图片及其所有相关数据
   */
  async deleteImage(imageId: string): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    const transaction = this.db.transaction(['images', 'chunks'], 'readwrite');

    // 删除元数据
    transaction.objectStore('images').delete(imageId);

    // 删除所有相关的文件块
    const chunkStore = transaction.objectStore('chunks');
    const index = chunkStore.index('imageId');
    const range = IDBKeyRange.only(imageId);

    return new Promise<void>((resolve, reject) => {
      const deleteRequest = index.openCursor(range);

      deleteRequest.onsuccess = event => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor) {
          cursor.delete();
          cursor.continue();
        } else {
          resolve();
        }
      };

      deleteRequest.onerror = () => reject(deleteRequest.error);
    });
  }

  /**
   * 获取所有图片的元数据
   */
  async getAllImageMetadata(): Promise<StoredImageMetadata[]> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    return new Promise<StoredImageMetadata[]>((resolve, reject) => {
      const transaction = this.db!.transaction(['images'], 'readonly');
      const store = transaction.objectStore('images');
      const request = store.getAll();

      request.onsuccess = () => {
        const result = request.result as StoredImageMetadata[];
        // 按时间戳排序，最新的在前面
        result.sort((a, b) => b.timestamp - a.timestamp);
        resolve(result);
      };

      request.onerror = () => reject(request.error);
    });
  }

  /**
   * 清理旧数据（保留最新的N张图片）
   */
  async cleanupOldImages(keepCount: number = 10): Promise<number> {
    const allMetadata = await this.getAllImageMetadata();

    if (allMetadata.length <= keepCount) {
      return 0;
    }

    const toDelete = allMetadata.slice(keepCount);
    let deletedCount = 0;

    for (const metadata of toDelete) {
      try {
        await this.deleteImage(metadata.id);
        deletedCount++;
      } catch (error) {
        console.error(`删除图片失败: ${metadata.id}`, error);
      }
    }

    return deletedCount;
  }

  // 私有方法

  private async saveFileInChunks(
    imageId: string,
    file: File,
    type: 'original' | 'processed',
    chunkPrefix: string = type
  ): Promise<number> {
    const totalChunks = Math.ceil(file.size / this.CHUNK_SIZE);

    for (let i = 0; i < totalChunks; i++) {
      const start = i * this.CHUNK_SIZE;
      const end = Math.min(start + this.CHUNK_SIZE, file.size);
      const chunk = file.slice(start, end);
      const arrayBuffer = await chunk.arrayBuffer();

      await this.saveChunk({
        id: `${imageId}_${chunkPrefix}_${i}`,
        imageId,
        chunkIndex: i,
        totalChunks,
        data: arrayBuffer,
        type,
      });
    }

    return totalChunks;
  }

  private async saveBlobUrlAsChunks(
    imageId: string,
    blobUrl: string,
    type: 'processed'
  ): Promise<number> {
    const response = await fetch(blobUrl);
    const blob = await response.blob();

    const totalChunks = Math.ceil(blob.size / this.CHUNK_SIZE);

    for (let i = 0; i < totalChunks; i++) {
      const start = i * this.CHUNK_SIZE;
      const end = Math.min(start + this.CHUNK_SIZE, blob.size);
      const chunk = blob.slice(start, end);
      const arrayBuffer = await chunk.arrayBuffer();

      await this.saveChunk({
        id: `${imageId}_${type}_${i}`,
        imageId,
        chunkIndex: i,
        totalChunks,
        data: arrayBuffer,
        type,
      });
    }

    return totalChunks;
  }

  private async saveChunk(chunk: FileChunk): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    return new Promise<void>((resolve, reject) => {
      const transaction = this.db!.transaction(['chunks'], 'readwrite');
      const store = transaction.objectStore('chunks');
      const request = store.put(chunk);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  private async saveMetadata(metadata: StoredImageMetadata): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    return new Promise<void>((resolve, reject) => {
      const transaction = this.db!.transaction(['images'], 'readwrite');
      const store = transaction.objectStore('images');
      const request = store.put(metadata);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  private async loadMetadata(
    imageId: string
  ): Promise<StoredImageMetadata | null> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    return new Promise<StoredImageMetadata | null>((resolve, reject) => {
      const transaction = this.db!.transaction(['images'], 'readonly');
      const store = transaction.objectStore('images');
      const request = store.get(imageId);

      request.onsuccess = () => {
        resolve(request.result || null);
      };
      request.onerror = () => reject(request.error);
    });
  }

  private async loadFileFromChunks(
    imageId: string,
    type: string,
    totalChunks: number,
    chunkPrefix: string = type,
    fileName?: string
  ): Promise<File> {
    const chunks: ArrayBuffer[] = [];

    for (let i = 0; i < totalChunks; i++) {
      const chunkId = `${imageId}_${chunkPrefix}_${i}`;
      const chunk = await this.loadChunk(chunkId);
      if (!chunk) {
        throw new Error(`文件块缺失: ${chunkId}`);
      }
      chunks.push(chunk.data);
    }

    const blob = new Blob(chunks);
    return new File([blob], fileName || `${imageId}_${type}`, {
      type: 'image/*',
    });
  }

  private async loadBlobFromChunks(
    imageId: string,
    type: string,
    totalChunks: number
  ): Promise<Blob> {
    const chunks: ArrayBuffer[] = [];

    for (let i = 0; i < totalChunks; i++) {
      const chunkId = `${imageId}_${type}_${i}`;
      const chunk = await this.loadChunk(chunkId);
      if (!chunk) {
        throw new Error(`文件块缺失: ${chunkId}`);
      }
      chunks.push(chunk.data);
    }

    return new Blob(chunks, { type: 'image/*' });
  }

  private async loadChunk(chunkId: string): Promise<FileChunk | null> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    return new Promise<FileChunk | null>((resolve, reject) => {
      const transaction = this.db!.transaction(['chunks'], 'readonly');
      const store = transaction.objectStore('chunks');
      const request = store.get(chunkId);

      request.onsuccess = () => {
        resolve(request.result || null);
      };
      request.onerror = () => reject(request.error);
    });
  }

  private async loadChunksByImageIdAndType(
    imageId: string,
    type: string
  ): Promise<FileChunk[]> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    return new Promise<FileChunk[]>((resolve, reject) => {
      const transaction = this.db!.transaction(['chunks'], 'readonly');
      const store = transaction.objectStore('chunks');
      const index = store.index('imageId');
      const range = IDBKeyRange.only(imageId);
      const request = index.getAll(range);

      request.onsuccess = () => {
        const allChunks = request.result as FileChunk[];
        const filteredChunks = allChunks.filter(chunk => chunk.type === type);
        resolve(filteredChunks);
      };
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * 保存当前选中的图片ID
   */
  async saveCurrentSelectedImageId(imageId: string | null): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    const appState: AppState = {
      id: 'current',
      currentSelectedImageId: imageId,
      timestamp: Date.now(),
    };

    return new Promise<void>((resolve, reject) => {
      const transaction = this.db!.transaction(['appState'], 'readwrite');
      const store = transaction.objectStore('appState');
      const request = store.put(appState);

      request.onsuccess = () => {
        resolve();
      };
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * 获取当前选中的图片ID
   */
  async getCurrentSelectedImageId(): Promise<string | null> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    return new Promise<string | null>((resolve, reject) => {
      const transaction = this.db!.transaction(['appState'], 'readonly');
      const store = transaction.objectStore('appState');
      const request = store.get('current');

      request.onsuccess = () => {
        const result = request.result as AppState;
        const selectedId = result?.currentSelectedImageId || null;

        resolve(selectedId);
      };
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * 保存自定义背景图片
   */
  async saveBackgroundImage(id: string, file: File): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    // 保存文件块
    const chunks = await this.saveFileInChunks(
      id,
      file,
      'original',
      'background'
    );

    // 准备元数据
    const metadata: StoredBackgroundImage = {
      id,
      name: file.name,
      size: file.size,
      timestamp: Date.now(),
      hasFile: true,
      fileChunks: chunks,
    };

    // 保存元数据
    await this.saveBackgroundMetadata(metadata);
  }

  /**
   * 加载自定义背景图片
   */
  async loadBackgroundImage(
    id: string
  ): Promise<{ file: File; url: string } | null> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    // 加载元数据
    const metadata = await this.loadBackgroundMetadata(id);
    if (!metadata || !metadata.hasFile || !metadata.fileChunks) {
      return null;
    }

    // 加载文件
    const file = await this.loadFileFromChunks(
      id,
      'original',
      metadata.fileChunks,
      'background',
      `background_${id}`
    );
    const url = URL.createObjectURL(file);

    return { file, url };
  }

  /**
   * 删除自定义背景图片
   */
  async deleteBackgroundImage(id: string): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    const transaction = this.db.transaction(
      ['backgroundImages', 'chunks'],
      'readwrite'
    );

    // 删除元数据
    transaction.objectStore('backgroundImages').delete(id);

    // 删除所有相关的文件块
    const chunkStore = transaction.objectStore('chunks');
    const index = chunkStore.index('imageId');
    const range = IDBKeyRange.only(id);

    return new Promise<void>((resolve, reject) => {
      const deleteRequest = index.openCursor(range);

      deleteRequest.onsuccess = event => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor) {
          cursor.delete();
          cursor.continue();
        } else {
          resolve();
        }
      };

      deleteRequest.onerror = () => reject(deleteRequest.error);
    });
  }

  /**
   * 获取所有自定义背景图片的元数据
   */
  async getAllBackgroundImages(): Promise<StoredBackgroundImage[]> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    return new Promise<StoredBackgroundImage[]>((resolve, reject) => {
      const transaction = this.db!.transaction(
        ['backgroundImages'],
        'readonly'
      );
      const store = transaction.objectStore('backgroundImages');
      const request = store.getAll();

      request.onsuccess = () => {
        const result = request.result as StoredBackgroundImage[];
        // 按时间戳排序，最新的在前面
        result.sort((a, b) => b.timestamp - a.timestamp);
        resolve(result);
      };

      request.onerror = () => reject(request.error);
    });
  }

  // 私有方法 - 背景图片相关

  /**
   * 通用的事务执行方法，包含错误处理
   */
  private executeTransaction<T>(
    storeNames: string | string[],
    mode: IDBTransactionMode,
    operation: (transaction: IDBTransaction) => Promise<T>
  ): Promise<T> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    return new Promise<T>((resolve, reject) => {
      const transaction = this.db!.transaction(storeNames, mode);

      transaction.onerror = () => reject(transaction.error);
      transaction.onabort = () => reject(new Error('事务被中止'));

      operation(transaction).then(resolve).catch(reject);
    });
  }

  private async saveBackgroundMetadata(
    metadata: StoredBackgroundImage
  ): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    return new Promise<void>((resolve, reject) => {
      const transaction = this.db!.transaction(
        ['backgroundImages'],
        'readwrite'
      );
      const store = transaction.objectStore('backgroundImages');
      const request = store.put(metadata);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  private async loadBackgroundMetadata(
    id: string
  ): Promise<StoredBackgroundImage | null> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    return new Promise<StoredBackgroundImage | null>((resolve, reject) => {
      const transaction = this.db!.transaction(
        ['backgroundImages'],
        'readonly'
      );
      const store = transaction.objectStore('backgroundImages');
      const request = store.get(id);

      request.onsuccess = () => {
        resolve(request.result || null);
      };
      request.onerror = () => reject(request.error);
    });
  }
}

// 导出单例实例
export const imageStorage = new ImageIndexedDBStorage();
